<!DOCTYPE html>
<!-- saved from url=(0045)https://www.seduo.cz/api/resources/index.html -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resources API Documentation - seduo.cz</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 30px;
            border-radius: 10px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 20px;
            z-index: 100;
            transition: all 0.3s ease;
        }

        .nav.scrolled {
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 10px;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .nav ul li {
            position: relative;
        }

        .submenu {
            display: none;
            position: absolute;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 15px;
            z-index: 1000;
            min-width: 200px;
            top: 100%;
            left: 0;
            border: 1px solid #e9ecef;
        }

        .nav ul li:hover .submenu {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .submenu li {
            margin: 0;
            width: 100%;
        }

        .submenu a {
            padding: 8px 12px;
            border-radius: 4px;
            display: block;
            width: 100%;
            box-sizing: border-box;
        }
        
        .nav a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        
        .nav a:hover {
            background-color: #f0f2ff;
        }
        
        .section {
            background: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .section h2 {
            color: #667eea;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #495057;
            font-size: 1.4rem;
            margin: 25px 0 15px 0;
        }
        
        .section h4 {
            color: #6c757d;
            font-size: 1.2rem;
            margin: 20px 0 10px 0;
        }
        
        .endpoint {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .method {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9rem;
            margin-right: 10px;
        }
        
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
            font-size: 0.85rem;
            line-height: 1.6;
            overflow: auto;
            margin: 20px 0;
            max-height: 500px;
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .code::before {
            content: attr(data-lang);
            position: absolute;
            top: 8px;
            right: 12px;
            background: rgba(0,0,0,0.1);
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: bold;
            text-transform: uppercase;
            opacity: 0.7;
        }

        .json {
            background: #1e1e1e;
            color: #d4d4d4;
            border: 1px solid #333;
        }

        .json::before {
            content: "JSON";
            background: rgba(255,255,255,0.1);
            color: #fff;
        }

        .xml {
            background: #2d3748;
            color: #e2e8f0;
            border: 1px solid #4a5568;
        }

        .xml::before {
            content: "XML";
            background: rgba(255,255,255,0.1);
            color: #fff;
        }

        .code.http {
            background: #f7fafc;
            color: #2d3748;
            border: 1px solid #cbd5e0;
        }

        .code.http::before {
            content: "HTTP";
            background: rgba(0,0,0,0.1);
            color: #2d3748;
        }

        /* Syntax highlighting for JSON */
        .json .string { color: #ce9178; }
        .json .number { color: #b5cea8; }
        .json .boolean { color: #569cd6; }
        .json .null { color: #569cd6; }
        .json .key { color: #9cdcfe; }

        /* Syntax highlighting for XML */
        .xml .tag { color: #92c5f7; }
        .xml .attr { color: #9cdcfe; }
        .xml .value { color: #ce9178; }
        
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            margin: 2px;
        }
        
        .badge-success { background: #d4edda; color: #155724; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-info { background: #d1ecf1; color: #0c5460; }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .downloads {
            display: flex;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .download-btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .download-btn:hover {
            background: #5a6fd8;
        }
        
        .download-btn.secondary {
            background: #6c757d;
        }
        
        .download-btn.secondary:hover {
            background: #5a6268;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .nav ul {
                flex-direction: column;
                gap: 10px;
            }

            .downloads {
                flex-direction: column;
            }

            .code {
                font-size: 0.8rem;
                padding: 15px;
                max-height: 400px;
            }

            .table {
                font-size: 0.9rem;
            }

            .table th,
            .table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Resources API Documentation</h1>
            <p>API for retrieving structured list of resources (courses) for companies with valid contracts</p>
        </div>

        <nav class="nav">
            <ul>
                <li><a href="#overview">API</a>
                    <ul class="submenu">
                        <li><a href="#overview">Overview</a></li>
                        <li><a href="#authentication">Authentication</a></li>
                        <li><a href="#endpoint">API Endpoint</a></li>
                        <li><a href="#data-structure">Data Structure</a></li>
                        <li><a href="#examples">Examples</a></li>
                        <li><a href="#errors">Error Codes</a></li>
                        <li><a href="#downloads">Downloads</a></li>
                    </ul>
                </li>
                <li><a href="#company-authentication">SSO</a></li>
                <li><a href="#xapi">xAPI</a></li>
            </ul>
        </nav>

        <section id="overview" class="section">
            <h2>Overview</h2>
            <p>The Resources API provides a structured list of resources (courses) for companies with valid contracts. The API is designed for external integration and provides complete course information including metadata.</p>
            
            <div class="alert alert-info">
                <strong>Important:</strong> This API is available only for companies with valid contracts and configured authorization tokens in administration.
            </div>

            <h3>Key Features</h3>
            <ul>
                <li><strong>Structured Data:</strong> Generation metadata with timestamp and resource types</li>
                <li><strong>Multi-format Support:</strong> Both XML and JSON response formats</li>
                <li><strong>Translations:</strong> Course data translated based on company domain</li>
                <li><strong>Security:</strong> Bearer token authentication required</li>
                <li><strong>Logging:</strong> All API access attempts are logged for monitoring</li>
                <li><strong>Daily Updates:</strong> Data is generated daily at midnight</li>
            </ul>
        </section>

        <section id="authentication" class="section">
            <h2>Authentication</h2>
            <p>The API uses Bearer token authentication. You must include a valid authorization token in the request header.</p>
            
            <h3>Requirements</h3>
            <ul>
                <li>Valid company contract</li>
                <li>Authorization token configured in company administration</li>
                <li>Bearer token in Authorization header</li>
            </ul>
            
            <h3>Header Format</h3>
            <div class="code">
Authorization: Bearer &lt;your-token-here&gt;
            </div>
            
            <div class="alert alert-warning">
                <strong>Security Note:</strong> Keep your authorization token secure and do not share it publicly.
            </div>
        </section>

        <section id="company-authentication" class="section">
            <h2>Company Authentication</h2>
            <p>Companies with landing pages can configure different authentication methods for their users. This affects how employees access courses through the company portal.</p>

            <div class="alert alert-info">
                <strong>Configuration:</strong> Authentication methods are configured in the company administration at <code>https://admin.seduo.com/seduocz/companies/{company_id}/authentication</code>
            </div>

            <h3>Authentication Types</h3>
            <p>Three authentication methods are available for companies:</p>

            <h4>1. Basic Authentication</h4>
            <div class="endpoint">
                <h4><span class="badge badge-info">Default</span> Basic Login</h4>
                <p>Standard email and password authentication. This is the default method when no specific authentication type is configured.</p>
            </div>

            <ul>
                <li><strong>Method:</strong> Email + Password</li>
                <li><strong>Configuration:</strong> No additional setup required</li>
                <li><strong>User Experience:</strong> Standard login form on company landing page</li>
                <li><strong>Security:</strong> Password-based authentication with optional password policies</li>
            </ul>

            <h4>2. Two-Factor Authentication (2FA Email)</h4>
            <div class="endpoint">
                <h4><span class="badge badge-warning">Enhanced</span> 2FA Email</h4>
                <p>Email-based two-factor authentication adds an extra security layer by sending verification codes to user's email.</p>
            </div>

            <ul>
                <li><strong>Method:</strong> Email + Password + Email Verification Code</li>
                <li><strong>Configuration:</strong> Set authentication type to <code>2fa-email</code></li>
                <li><strong>User Experience:</strong> After login, users receive email with verification code</li>
                <li><strong>Security:</strong> Enhanced security with email-based second factor</li>
                <li><strong>Code Validity:</strong> Verification codes expire after a set time period</li>
            </ul>

            <h4>3. Single Sign-On (SSO Azure)</h4>
            <div class="endpoint">
                <h4><span class="badge badge-success">Enterprise</span> SSO Azure (SAML2)</h4>
                <p>Enterprise-grade Single Sign-On integration with Azure Active Directory using SAML2 protocol.</p>
            </div>

            <ul>
                <li><strong>Method:</strong> SAML2-based SSO with Azure AD</li>
                <li><strong>Configuration:</strong> Requires Azure AD setup and SAML2 configuration</li>
                <li><strong>User Experience:</strong> Seamless login through company's Azure AD</li>
                <li><strong>Security:</strong> Enterprise-grade security managed by Azure AD</li>
                <li><strong>User Management:</strong> Centralized user management through Azure AD</li>
            </ul>

            <h3>SAML2 Configuration</h3>
            <p>For companies using SSO Azure authentication, the following SAML2 configuration is required:</p>

            <h4>Required Configuration Parameters</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Description</th>
                        <th>Example</th>
                        <th>Required</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>ssoIdpUrl</code></td>
                        <td>Azure AD Identity Provider URL</td>
                        <td><code>https://login.microsoftonline.com/{tenant-id}/saml2</code></td>
                        <td><span class="badge badge-danger">Yes</span></td>
                    </tr>
                    <tr>
                        <td><code>ssoEmailAttribute</code></td>
                        <td>SAML attribute containing user email</td>
                        <td><code>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</code></td>
                        <td><span class="badge badge-danger">Yes</span></td>
                    </tr>
                    <tr>
                        <td><code>ssoNameAttribute</code></td>
                        <td>SAML attribute containing user first name</td>
                        <td><code>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname</code></td>
                        <td><span class="badge badge-warning">Optional</span></td>
                    </tr>
                    <tr>
                        <td><code>ssoSurnameAttribute</code></td>
                        <td>SAML attribute containing user surname</td>
                        <td><code>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname</code></td>
                        <td><span class="badge badge-warning">Optional</span></td>
                    </tr>
                </tbody>
            </table>

            <h4>SAML2 Metadata</h4>
            <p>Each company with SSO authentication has its own SAML2 metadata endpoint:</p>

            <div class="code">
GET /company/{company-slug}/saml2/metadata
            </div>

            <p>This endpoint provides the Service Provider (SP) metadata that needs to be configured in Azure AD.</p>

            <h4>SAML2 Authentication Flow</h4>
            <ol>
                <li><strong>User Access:</strong> User visits company landing page</li>
                <li><strong>SSO Redirect:</strong> System detects SSO configuration and redirects to Azure AD</li>
                <li><strong>Azure Authentication:</strong> User authenticates with Azure AD credentials</li>
                <li><strong>SAML Response:</strong> Azure AD sends SAML response with user attributes</li>
                <li><strong>User Verification:</strong> System verifies user exists in company or initiates registration</li>
                <li><strong>Login Complete:</strong> User is logged in and redirected to courses</li>
            </ol>

            <h3>Company Landing Page Requirements</h3>
            <div class="alert alert-warning">
                <strong>Prerequisites:</strong> To use any authentication method, companies must have:
                <ul style="margin-top: 10px;">
                    <li>Active contract with seduo.cz</li>
                    <li>Configured company landing page</li>
                    <li>Valid authorization token for API access</li>
                </ul>
            </div>

            <h4>Landing Page URL Structure</h4>
            <div class="code">
https://www.seduo.cz/company/{company-slug}
            </div>

            <p>Where <code>{company-slug}</code> is the unique identifier for the company's landing page.</p>

            <h3>User Registration Flow</h3>
            <p>When users don't exist in the system, different registration flows are available:</p>

            <ul>
                <li><strong>Self-Registration:</strong> Automatic user registration for allowed domains</li>
                <li><strong>Manual Approval:</strong> Registration requests require admin approval</li>
                <li><strong>SSO Registration:</strong> Automatic registration from SAML attributes</li>
                <li><strong>Invitation System:</strong> Email invitations for existing users without passwords</li>
            </ul>
        </section>

        <section id="endpoint" class="section">
            <h2>API Endpoint</h2>

            <div class="endpoint">
                <h3><span class="method">POST</span>/api/resources/{format}</h3>
                <p>Get structured list of resources (courses) for companies with valid contracts.</p>
            </div>

            <h3>Parameters</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Required</th>
                        <th>Description</th>
                        <th>Values</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>format</code></td>
                        <td>Path</td>
                        <td><span class="badge badge-danger">Yes</span></td>
                        <td>Response format</td>
                        <td><code>xml</code>, <code>json</code></td>
                    </tr>
                    <tr>
                        <td><code>Authorization</code></td>
                        <td>Header</td>
                        <td><span class="badge badge-danger">Yes</span></td>
                        <td>Bearer token for authentication</td>
                        <td><code>Bearer &lt;token&gt;</code></td>
                    </tr>
                </tbody>
            </table>

            <h3>Response Codes</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Code</th>
                        <th>Status</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="badge badge-success">200</span></td>
                        <td>Success</td>
                        <td>Successful result - structured list of resources</td>
                    </tr>
                    <tr>
                        <td><span class="badge badge-warning">400</span></td>
                        <td>Bad Request</td>
                        <td>Invalid format parameter</td>
                    </tr>
                    <tr>
                        <td><span class="badge badge-danger">401</span></td>
                        <td>Unauthorized</td>
                        <td>Missing or invalid Bearer token</td>
                    </tr>
                    <tr>
                        <td><span class="badge badge-danger">403</span></td>
                        <td>Forbidden</td>
                        <td>Company does not have valid contract or authorization token not configured</td>
                    </tr>
                    <tr>
                        <td><span class="badge badge-danger">500</span></td>
                        <td>Server Error</td>
                        <td>Internal server error</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section id="data-structure" class="section">
            <h2>Data Structure</h2>

            <h3>Response Structure</h3>
            <p>The API returns a structured response containing metadata and course resources.</p>

            <h4>Metadata</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>generated_at</code></td>
                        <td>string (datetime)</td>
                        <td>Timestamp when the resources were last generated (daily at midnight)</td>
                    </tr>
                    <tr>
                        <td><code>resource_types</code></td>
                        <td>array</td>
                        <td>Available resource types with descriptions</td>
                    </tr>
                </tbody>
            </table>

            <h4>Course Resource Fields</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Field</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>resource_type</code></td>
                        <td>string</td>
                        <td>Type of the resource (standard, microlearning)</td>
                    </tr>
                    <tr>
                        <td><code>course_name</code></td>
                        <td>string</td>
                        <td>Name of the course (translated based on company domain)</td>
                    </tr>
                    <tr>
                        <td><code>lecturers</code></td>
                        <td>array</td>
                        <td>List of lecturer names</td>
                    </tr>
                    <tr>
                        <td><code>categories</code></td>
                        <td>array</td>
                        <td>List of category names (translated based on company domain)</td>
                    </tr>
                    <tr>
                        <td><code>abilities</code></td>
                        <td>array</td>
                        <td>List of ability/skill names (translated based on company domain)</td>
                    </tr>
                    <tr>
                        <td><code>course_duration</code></td>
                        <td>integer</td>
                        <td>Course duration in minutes</td>
                    </tr>
                    <tr>
                        <td><code>course_id</code></td>
                        <td>integer</td>
                        <td>Unique course identifier</td>
                    </tr>
                    <tr>
                        <td><code>image_url</code></td>
                        <td>string (uri)</td>
                        <td>URL to the course image</td>
                    </tr>
                    <tr>
                        <td><code>video_url</code></td>
                        <td>string (uri)</td>
                        <td>URL to the course video</td>
                    </tr>
                    <tr>
                        <td><code>claim</code></td>
                        <td>string</td>
                        <td>Course claim/slogan (translated based on company domain)</td>
                    </tr>
                    <tr>
                        <td><code>course_description</code></td>
                        <td>string</td>
                        <td>Course description (translated based on company domain)</td>
                    </tr>
                    <tr>
                        <td><code>video_language</code></td>
                        <td>string</td>
                        <td>Language code of the video content (e.g., "cs", "en")</td>
                    </tr>
                </tbody>
            </table>

            <h3>Translations</h3>
            <p>Texts are automatically translated based on company domain:</p>
            <ul>
                <li><code>seduo.cz</code> → Czech translations</li>
                <li><code>seduo.sk</code> → Slovak translations</li>
                <li><code>seduo.pl</code> → Polish translations</li>
                <li><code>seduo.hu</code> → Hungarian translations</li>
            </ul>
        </section>

        <section id="examples" class="section">
            <h2>Examples</h2>

            <h3>Request Example</h3>
            <div class="code http">
<strong>POST</strong> /api/resources/json HTTP/1.1
<strong>Host:</strong> www.seduo.cz
<strong>Authorization:</strong> Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
<strong>Content-Type:</strong> application/json
            </div>

            <h3>JSON Response Example</h3>
            <div class="code json">
{
  <span class="key">"metadata"</span>: {
    <span class="key">"generated_at"</span>: <span class="string">"2024-01-15T00:30:00Z"</span>,
    <span class="key">"resource_types"</span>: [
      {
        <span class="key">"type"</span>: <span class="string">"standard"</span>,
        <span class="key">"description"</span>: <span class="string">"Standard courses"</span>
      },
      {
        <span class="key">"type"</span>: <span class="string">"microlearning"</span>,
        <span class="key">"description"</span>: <span class="string">"Microlearning courses"</span>
      }
    ]
  },

  <span class="key">"resources"</span>: [
    {
      <span class="key">"resource_type"</span>: <span class="string">"standard"</span>,
      <span class="key">"course_name"</span>: <span class="string">"Effective Communication"</span>,

      <span class="key">"lecturers"</span>: [
        <span class="string">"John Smith"</span>,
        <span class="string">"Jane Doe"</span>
      ],

      <span class="key">"categories"</span>: [
        <span class="string">"Communication"</span>,
        <span class="string">"Soft Skills"</span>
      ],

      <span class="key">"abilities"</span>: [
        <span class="string">"Active Listening"</span>,
        <span class="string">"Public Speaking"</span>
      ],

      <span class="key">"course_duration"</span>: <span class="number">120</span>,
      <span class="key">"course_id"</span>: <span class="number">1234</span>,

      <span class="key">"image_url"</span>: <span class="string">"https://seduocz.educdn.cz/static/images/1234/effective-communication.jpg"</span>,
      <span class="key">"video_url"</span>: <span class="string">"https://video.seduo.cz/v/abc123def"</span>,

      <span class="key">"claim"</span>: <span class="string">"Master the art of effective communication"</span>,
      <span class="key">"course_description"</span>: <span class="string">"Learn essential communication skills for professional success"</span>,
      <span class="key">"video_language"</span>: <span class="string">"cs"</span>
    }
  ]
}
            </div>

            <h3>XML Response Example</h3>
            <div class="code xml">
<span class="tag">&lt;?xml version="1.0" encoding="UTF-8"?&gt;</span>
<span class="tag">&lt;resources&gt;</span>
  <span class="tag">&lt;metadata&gt;</span>
    <span class="tag">&lt;generated_at&gt;</span><span class="value">2024-01-15T00:30:00Z</span><span class="tag">&lt;/generated_at&gt;</span>
    <span class="tag">&lt;resource_types&gt;</span>
      <span class="tag">&lt;resource_type&gt;</span>
        <span class="tag">&lt;type&gt;</span><span class="value">standard</span><span class="tag">&lt;/type&gt;</span>
        <span class="tag">&lt;description&gt;</span><span class="value">Standard courses</span><span class="tag">&lt;/description&gt;</span>
      <span class="tag">&lt;/resource_type&gt;</span>
      <span class="tag">&lt;resource_type&gt;</span>
        <span class="tag">&lt;type&gt;</span><span class="value">microlearning</span><span class="tag">&lt;/type&gt;</span>
        <span class="tag">&lt;description&gt;</span><span class="value">Microlearning courses</span><span class="tag">&lt;/description&gt;</span>
      <span class="tag">&lt;/resource_type&gt;</span>
    <span class="tag">&lt;/resource_types&gt;</span>
  <span class="tag">&lt;/metadata&gt;</span>

  <span class="tag">&lt;resource_list&gt;</span>
    <span class="tag">&lt;resource&gt;</span>
      <span class="tag">&lt;resource_type&gt;</span><span class="value">standard</span><span class="tag">&lt;/resource_type&gt;</span>
      <span class="tag">&lt;course_name&gt;</span><span class="value">Effective Communication</span><span class="tag">&lt;/course_name&gt;</span>

      <span class="tag">&lt;lecturers&gt;</span>
        <span class="tag">&lt;lecturer&gt;</span><span class="value">John Smith</span><span class="tag">&lt;/lecturer&gt;</span>
        <span class="tag">&lt;lecturer&gt;</span><span class="value">Jane Doe</span><span class="tag">&lt;/lecturer&gt;</span>
      <span class="tag">&lt;/lecturers&gt;</span>

      <span class="tag">&lt;categories&gt;</span>
        <span class="tag">&lt;category&gt;</span><span class="value">Communication</span><span class="tag">&lt;/category&gt;</span>
        <span class="tag">&lt;category&gt;</span><span class="value">Soft Skills</span><span class="tag">&lt;/category&gt;</span>
      <span class="tag">&lt;/categories&gt;</span>

      <span class="tag">&lt;abilities&gt;</span>
        <span class="tag">&lt;ability&gt;</span><span class="value">Active Listening</span><span class="tag">&lt;/ability&gt;</span>
        <span class="tag">&lt;ability&gt;</span><span class="value">Public Speaking</span><span class="tag">&lt;/ability&gt;</span>
      <span class="tag">&lt;/abilities&gt;</span>

      <span class="tag">&lt;course_duration&gt;</span><span class="value">120</span><span class="tag">&lt;/course_duration&gt;</span>
      <span class="tag">&lt;course_id&gt;</span><span class="value">1234</span><span class="tag">&lt;/course_id&gt;</span>
      <span class="tag">&lt;image_url&gt;</span><span class="value">https://seduocz.educdn.cz/static/images/1234/effective-communication.jpg</span><span class="tag">&lt;/image_url&gt;</span>
      <span class="tag">&lt;video_url&gt;</span><span class="value">https://video.seduo.cz/v/abc123def</span><span class="tag">&lt;/video_url&gt;</span>
      <span class="tag">&lt;claim&gt;</span><span class="value">Master the art of effective communication</span><span class="tag">&lt;/claim&gt;</span>
      <span class="tag">&lt;course_description&gt;</span><span class="value">Learn essential communication skills for professional success</span><span class="tag">&lt;/course_description&gt;</span>
      <span class="tag">&lt;video_language&gt;</span><span class="value">cs</span><span class="tag">&lt;/video_language&gt;</span>
    <span class="tag">&lt;/resource&gt;</span>
  <span class="tag">&lt;/resource_list&gt;</span>
<span class="tag">&lt;/resources&gt;</span>
            </div>

            <h3>SAML2 Metadata Example</h3>
            <p>Example of SAML2 Service Provider metadata for company SSO configuration:</p>

            <div class="code xml">
<span class="tag">&lt;?xml version="1.0" encoding="UTF-8"?&gt;</span>
<span class="tag">&lt;md:EntityDescriptor</span> <span class="attr">xmlns:md</span>=<span class="value">"urn:oasis:names:tc:SAML:2.0:metadata"</span>
                     <span class="attr">entityID</span>=<span class="value">"https://www.seduo.cz/company/example-company/saml2/metadata"</span><span class="tag">&gt;</span>

  <span class="tag">&lt;md:SPSSODescriptor</span> <span class="attr">protocolSupportEnumeration</span>=<span class="value">"urn:oasis:names:tc:SAML:2.0:protocol"</span>
                      <span class="attr">AuthnRequestsSigned</span>=<span class="value">"false"</span>
                      <span class="attr">WantAssertionsSigned</span>=<span class="value">"false"</span><span class="tag">&gt;</span>

    <span class="tag">&lt;md:KeyDescriptor</span> <span class="attr">use</span>=<span class="value">"signing"</span><span class="tag">&gt;</span>
      <span class="tag">&lt;ds:KeyInfo</span> <span class="attr">xmlns:ds</span>=<span class="value">"http://www.w3.org/2000/09/xmldsig#"</span><span class="tag">&gt;</span>
        <span class="tag">&lt;ds:KeyInfo&gt;</span>
            <span class="tag">&lt;ds:X509Data&gt;</span>
                <span class="tag">&lt;ds:X509Certificate&gt;</span><span class="value">MIICertificateDataHere...</span><span class="tag">&lt;/ds:X509Certificate&gt;</span>
            <span class="tag">&lt;/ds:X509Data&gt;</span>
        <span class="tag">&lt;/ds:KeyInfo&gt;</span>
    <span class="tag">&lt;/md:KeyDescriptor&gt;</span>

    <span class="tag">&lt;md:SingleLogoutService</span> <span class="attr">Binding</span>=<span class="value">"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"</span>
                            <span class="attr">Location</span>=<span class="value">"https://www.seduo.cz/company/example-company/logout"</span> <span class="tag">/&gt;</span>

    <span class="tag">&lt;md:NameIDFormat&gt;</span><span class="value">urn:oasis:names:tc:SAML:2.0:nameid-format:persistent</span><span class="tag">&lt;/md:NameIDFormat&gt;</span>

    <span class="tag">&lt;md:AssertionConsumerService</span> <span class="attr">Binding</span>=<span class="value">"urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"</span>
                                <span class="attr">Location</span>=<span class="value">"https://www.seduo.cz/company/example-company/login_check"</span>
                                <span class="attr">index</span>=<span class="value">"1"</span> <span class="tag">/&gt;</span>
  <span class="tag">&lt;/md:SPSSODescriptor&gt;</span>
<span class="tag">&lt;/md:EntityDescriptor&gt;</span>
            </div>

            <h3>SAML2 Authentication Request Example</h3>
            <p>When redirecting to Azure AD, the system generates a SAML AuthnRequest:</p>

            <div class="code http">
<strong>POST</strong> https://login.microsoftonline.com/{tenant-id}/saml2 HTTP/1.1
<strong>Content-Type:</strong> application/x-www-form-urlencoded

<strong>SAMLRequest=</strong>PHNhbWxwOkF1dGhuUmVxdWVzdCB4bWxuczpzYW1scD0idXJuOm9hc2lzOm5hbWVzOnRjOlNBTUw6Mi4wOnByb3RvY29sIi4uLg==
            </div>

            <h3>Azure AD Configuration</h3>
            <p>To configure Azure AD for SAML2 SSO with seduo.cz:</p>

            <ol>
                <li><strong>Create Enterprise Application:</strong> Add new enterprise application in Azure AD</li>
                <li><strong>Configure SAML:</strong> Set up SAML-based sign-on</li>
                <li><strong>Set Identifier:</strong> Use company's metadata URL as identifier</li>
                <li><strong>Set Reply URL:</strong> Use company's login_check URL</li>
                <li><strong>Configure Claims:</strong> Map user attributes to SAML claims</li>
                <li><strong>Assign Users:</strong> Assign company users to the application</li>
            </ol>

            <h4>Required Azure AD Claims Mapping</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Claim Name</th>
                        <th>Source Attribute</th>
                        <th>Purpose</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress</code></td>
                        <td><code>user.mail</code></td>
                        <td>User identification and login</td>
                    </tr>
                    <tr>
                        <td><code>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname</code></td>
                        <td><code>user.givenname</code></td>
                        <td>User first name (optional)</td>
                    </tr>
                    <tr>
                        <td><code>http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname</code></td>
                        <td><code>user.surname</code></td>
                        <td>User last name (optional)</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <section id="xapi" class="section">
            <h2>xAPI Integration</h2>
            <p>The Resources API integrates with the <a href="https://xapi.com/" target="_blank">Experience API (xAPI)</a> standard to track learning activities and generate standardized learning statements when students complete courses.</p>

            <div class="alert alert-info">
                <strong>xAPI Standard:</strong> The Experience API (xAPI) is a specification for learning technology that makes it possible to collect data about the wide range of experiences a person has (online and offline).
            </div>

            <h3>How xAPI Works with Resources API</h3>
            <p>When a student completes a course obtained through the Resources API, the system automatically generates xAPI statements that conform to the xAPI specification. These statements provide detailed information about the learning experience.</p>

            <h3>xAPI Statement Structure</h3>
            <p>Each xAPI statement follows the pattern: <strong>Actor</strong> + <strong>Verb</strong> + <strong>Object</strong> + <strong>Result</strong></p>

            <h4>Course Completion Statement Example</h4>
            <p>Here's an example of an xAPI statement generated when a student completes a course:</p>

            <div class="code json">
{
  <span class="key">"id"</span>: <span class="string">"12345678-1234-5678-1234-123456789012"</span>,
  <span class="key">"timestamp"</span>: <span class="string">"2024-01-15T14:30:00.000Z"</span>,

  <span class="key">"actor"</span>: {
    <span class="key">"name"</span>: <span class="string">"John Smith"</span>,
    <span class="key">"mbox"</span>: <span class="string">"mailto:<EMAIL>"</span>,
    <span class="key">"objectType"</span>: <span class="string">"Agent"</span>
  },

  <span class="key">"verb"</span>: {
    <span class="key">"id"</span>: <span class="string">"http://adlnet.gov/expapi/verbs/completed"</span>,
    <span class="key">"display"</span>: {
      <span class="key">"en-US"</span>: <span class="string">"completed"</span>,
      <span class="key">"cs"</span>: <span class="string">"dokončil"</span>
    }
  },

  <span class="key">"object"</span>: {
    <span class="key">"id"</span>: <span class="string">"https://www.seduo.cz/courses/1234"</span>,
    <span class="key">"definition"</span>: {
      <span class="key">"name"</span>: {
        <span class="key">"en-US"</span>: <span class="string">"Effective Communication"</span>,
        <span class="key">"cs"</span>: <span class="string">"Efektivní komunikace"</span>
      },

      <span class="key">"description"</span>: {
        <span class="key">"en-US"</span>: <span class="string">"Learn essential communication skills for professional success"</span>,
        <span class="key">"cs"</span>: <span class="string">"Naučte se základní komunikační dovednosti pro profesní úspěch"</span>
      },

      <span class="key">"type"</span>: <span class="string">"http://adlnet.gov/expapi/activities/course"</span>,

      <span class="key">"extensions"</span>: {
        <span class="key">"https://www.seduo.cz/xapi/extensions/course-id"</span>: <span class="number">1234</span>,
        <span class="key">"https://www.seduo.cz/xapi/extensions/resource-type"</span>: <span class="string">"standard"</span>,
        <span class="key">"https://www.seduo.cz/xapi/extensions/duration"</span>: <span class="number">120</span>,

        <span class="key">"https://www.seduo.cz/xapi/extensions/categories"</span>: [
          <span class="string">"Communication"</span>,
          <span class="string">"Soft Skills"</span>
        ],

        <span class="key">"https://www.seduo.cz/xapi/extensions/abilities"</span>: [
          <span class="string">"Active Listening"</span>,
          <span class="string">"Public Speaking"</span>
        ],

        <span class="key">"https://www.seduo.cz/xapi/extensions/lecturers"</span>: [
          <span class="string">"John Smith"</span>,
          <span class="string">"Jane Doe"</span>
        ]
      }
    },
    <span class="key">"objectType"</span>: <span class="string">"Activity"</span>
  },

  <span class="key">"result"</span>: {
    <span class="key">"completion"</span>: <span class="boolean">true</span>,
    <span class="key">"success"</span>: <span class="boolean">true</span>,

    <span class="key">"score"</span>: {
      <span class="key">"scaled"</span>: <span class="number">0.85</span>,
      <span class="key">"raw"</span>: <span class="number">85</span>,
      <span class="key">"min"</span>: <span class="number">0</span>,
      <span class="key">"max"</span>: <span class="number">100</span>
    },

    <span class="key">"duration"</span>: <span class="string">"PT1H45M30S"</span>,

    <span class="key">"extensions"</span>: {
      <span class="key">"https://www.seduo.cz/xapi/extensions/completion-date"</span>: <span class="string">"2024-01-15T14:30:00.000Z"</span>,
      <span class="key">"https://www.seduo.cz/xapi/extensions/attempts"</span>: <span class="number">1</span>,
      <span class="key">"https://www.seduo.cz/xapi/extensions/time-spent"</span>: <span class="string">"PT1H45M30S"</span>
    }
  },

  <span class="key">"context"</span>: {
    <span class="key">"platform"</span>: <span class="string">"seduo.cz"</span>,
    <span class="key">"language"</span>: <span class="string">"cs"</span>,

    <span class="key">"extensions"</span>: {
      <span class="key">"https://www.seduo.cz/xapi/extensions/company-domain"</span>: <span class="string">"company.seduo.cz"</span>,
      <span class="key">"https://www.seduo.cz/xapi/extensions/learning-path"</span>: <span class="string">"Professional Development"</span>
    }
  }
}
            </div>

            <h3>xAPI Statement Components</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Component</th>
                        <th>Description</th>
                        <th>Example Value</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Actor</strong></td>
                        <td>The learner who performed the activity</td>
                        <td>John Smith (<EMAIL>)</td>
                    </tr>
                    <tr>
                        <td><strong>Verb</strong></td>
                        <td>The action that was performed</td>
                        <td>completed, experienced, passed, failed</td>
                    </tr>
                    <tr>
                        <td><strong>Object</strong></td>
                        <td>The activity that was performed on</td>
                        <td>Course: "Effective Communication"</td>
                    </tr>
                    <tr>
                        <td><strong>Result</strong></td>
                        <td>The outcome of the activity</td>
                        <td>Score: 85%, Duration: 1h 45m, Success: true</td>
                    </tr>
                    <tr>
                        <td><strong>Context</strong></td>
                        <td>Additional context information</td>
                        <td>Platform: seduo.cz, Language: cs</td>
                    </tr>
                </tbody>
            </table>

            <h3>Supported xAPI Verbs</h3>
            <p>The system supports various xAPI verbs depending on the learning activity:</p>
            <ul>
                <li><strong>completed</strong> - Student finished the entire course</li>
                <li><strong>passed</strong> - Student successfully passed the course assessment</li>
                <li><strong>failed</strong> - Student did not pass the course assessment</li>
                <li><strong>experienced</strong> - Student accessed/viewed course content</li>
                <li><strong>attempted</strong> - Student started an assessment or activity</li>
                <li><strong>progressed</strong> - Student made progress through the course</li>
            </ul>

            <h3>xAPI Extensions</h3>
            <p>Seduo.cz uses custom xAPI extensions to provide additional course-specific data:</p>
            <table class="table">
                <thead>
                    <tr>
                        <th>Extension</th>
                        <th>Description</th>
                        <th>Data Type</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>course-id</code></td>
                        <td>Unique course identifier from Resources API</td>
                        <td>integer</td>
                    </tr>
                    <tr>
                        <td><code>resource-type</code></td>
                        <td>Type of resource (standard, microlearning)</td>
                        <td>string</td>
                    </tr>
                    <tr>
                        <td><code>duration</code></td>
                        <td>Expected course duration in minutes</td>
                        <td>integer</td>
                    </tr>
                    <tr>
                        <td><code>categories</code></td>
                        <td>Course categories from Resources API</td>
                        <td>array</td>
                    </tr>
                    <tr>
                        <td><code>abilities</code></td>
                        <td>Skills/abilities from Resources API</td>
                        <td>array</td>
                    </tr>
                    <tr>
                        <td><code>lecturers</code></td>
                        <td>Course lecturers from Resources API</td>
                        <td>array</td>
                    </tr>
                    <tr>
                        <td><code>company-domain</code></td>
                        <td>Company's seduo.cz domain</td>
                        <td>string</td>
                    </tr>
                </tbody>
            </table>

            <h3>Integration Benefits</h3>
            <ul>
                <li><strong>Standardized Tracking:</strong> Consistent learning data across all systems</li>
                <li><strong>Interoperability:</strong> Data can be shared with other xAPI-compliant systems</li>
                <li><strong>Analytics:</strong> Rich learning analytics and reporting capabilities</li>
                <li><strong>Compliance:</strong> Meets industry standards for learning data</li>
                <li><strong>Detailed Insights:</strong> Comprehensive learning activity tracking</li>
            </ul>

            <div class="alert alert-info">
                <strong>Learn More:</strong> For detailed information about the xAPI standard, visit <a href="https://xapi.com/" target="_blank">xapi.com</a> or the <a href="https://github.com/adlnet/xAPI-Spec" target="_blank">official xAPI specification</a>.
            </div>
        </section>

        <section id="errors" class="section">
            <h2>Error Codes</h2>
            <p>The API returns standard HTTP status codes along with descriptive error messages.</p>

            <h3>Error Response Format</h3>
            <div class="code json">
{
  <span class="key">"error"</span>: <span class="string">"Error description message"</span>
}
            </div>

            <h3>Common Error Scenarios</h3>

            <h4><span class="badge badge-warning">400</span> Bad Request</h4>
            <div class="code json">
{
  <span class="key">"error"</span>: <span class="string">"Invalid format parameter. Allowed values: xml, json"</span>
}
            </div>

            <h4><span class="badge badge-danger">401</span> Unauthorized</h4>
            <div class="code json">
{
  <span class="key">"error"</span>: <span class="string">"Authorization token is required"</span>
}
            </div>

            <div class="code json">
{
  <span class="key">"error"</span>: <span class="string">"Invalid or expired authorization token"</span>
}
            </div>

            <h4><span class="badge badge-danger">403</span> Forbidden</h4>
            <div class="code json">
{
  <span class="key">"error"</span>: <span class="string">"Company does not have a valid contract"</span>
}
            </div>

            <div class="code json">
{
  <span class="key">"error"</span>: <span class="string">"Authorization token not configured in administration"</span>
}
            </div>

            <h4><span class="badge badge-danger">500</span> Internal Server Error</h4>
            <div class="code json">
{
  <span class="key">"error"</span>: <span class="string">"Internal server error occurred"</span>
}
            </div>
        </section>

        <section id="downloads" class="section">
            <h2>Downloads</h2>
            <p>Download the API documentation in various formats for integration with your tools.</p>

            <div class="downloads">
                <a href="https://www.seduo.cz/api/resources/swagger.yaml" class="download-btn" download="">
                    📄 Download YAML (OpenAPI 3.0.1)
                </a>
                <a href="https://www.seduo.cz/api/resources/swagger.json" class="download-btn secondary" download="">
                    📄 Download JSON (Swagger 2.0)
                </a>
            </div>

            <h3>Integration Tools</h3>

            <h4>Swagger UI</h4>
            <p>View and test the API in Swagger UI:</p>
            <ol>
                <li>Open <a href="https://editor.swagger.io/" target="_blank">https://editor.swagger.io/</a></li>
                <li>Import the YAML or JSON file</li>
                <li>Browse the documentation and test the API</li>
            </ol>

            <h4>Postman</h4>
            <p>Import into Postman for testing:</p>
            <ol>
                <li>Download the JSON file</li>
                <li>Open Postman and click "Import"</li>
                <li>Select the downloaded JSON file</li>
                <li>Configure Bearer token in Authorization tab</li>
                <li>Test the endpoint with different formats</li>
            </ol>

            <h4>Code Generation</h4>
            <p>Generate client libraries using tools like:</p>
            <ul>
                <li><strong>OpenAPI Generator:</strong> <a href="https://openapi-generator.tech/" target="_blank">https://openapi-generator.tech/</a></li>
                <li><strong>Swagger Codegen:</strong> <a href="https://swagger.io/tools/swagger-codegen/" target="_blank">https://swagger.io/tools/swagger-codegen/</a></li>
            </ul>
        </section>

        <section class="section">
            <h2>Contact &amp; Support</h2>
            <p>For technical questions, integration support, or access requests, please contact the seduo.cz team.</p>

            <div class="alert alert-info">
                <strong>Need Access?</strong> To obtain API access, you need a valid company contract and authorization token configuration in administration. Contact your account manager for details.
            </div>

            <h3>Authentication Configuration Support</h3>
            <p>For assistance with company authentication setup:</p>
            <ul>
                <li><strong>Basic &amp; 2FA Setup:</strong> Contact your account manager</li>
                <li><strong>SSO/SAML2 Configuration:</strong> Technical support team can assist with Azure AD integration</li>
                <li><strong>Landing Page Setup:</strong> Required before authentication configuration</li>
                <li><strong>User Management:</strong> Support for user registration and access control</li>
            </ul>

            <h3>Additional Resources</h3>
            <ul>
                <li><strong>Company Portal:</strong> <a href="https://www.seduo.cz/" target="_blank">seduo.cz</a></li>
                <li><strong>Admin Panel:</strong> <a href="https://admin.seduo.com/" target="_blank">admin.seduo.com</a></li>
                <li><strong>xAPI Specification:</strong> <a href="https://xapi.com/" target="_blank">xapi.com</a></li>
                <li><strong>SAML2 Documentation:</strong> <a href="https://docs.oasis-open.org/security/saml/Post2.0/sstc-saml-tech-overview-2.0.html" target="_blank">OASIS SAML2 Overview</a></li>
                <li><strong>Azure AD SAML:</strong> <a href="https://docs.microsoft.com/en-us/azure/active-directory/manage-apps/configure-saml-single-sign-on" target="_blank">Microsoft Azure AD SAML Configuration</a></li>
            </ul>
        </section>

        <footer style="text-align: center; padding: 40px 0; color: #6c757d; border-top: 1px solid #e9ecef; margin-top: 40px;">
            <p>© 2024 seduo.cz - Resources API Documentation</p>
            <p>Generated on <span id="current-date">June 11, 2025</span></p>
        </footer>
    </div>

    <script>
        // Set current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Highlight current section in navigation and handle sticky nav
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section[id]');
            const navLinks = document.querySelectorAll('.nav a[href^="#"]');
            const nav = document.querySelector('.nav');

            // Add scrolled class to nav for enhanced shadow
            if (window.pageYOffset > 100) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 150;
                if (window.pageYOffset >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.style.backgroundColor = '';
                if (link.getAttribute('href') === '#' + current) {
                    link.style.backgroundColor = '#f0f2ff';
                }
            });
        });
    </script>


</body></html>
